<?php
// 引入数据库配置文件
require '../db.php';

// 获取动态参数 token 和 kami
if (isset($_GET['token']) && isset($_GET['kami'])) {
    $token = $_GET['token'];
    $kami = $_GET['kami'];

    // 检查 token 指定的用户是否存在
    $stmt = $mysqli->prepare("SELECT token, vipcode, viptime FROM users WHERE token = ?");
    $stmt->bind_param("s", $token);
    $stmt->execute();
    $user_result = $stmt->get_result();
    
    if ($user_result->num_rows == 0) {
        echo json_encode(["code" => 400, "message" => "用户未注册"], JSON_UNESCAPED_UNICODE);
        exit();
    }

    $user = $user_result->fetch_assoc();

    // 检查 kami 指定的卡密是否存在
    $stmt = $mysqli->prepare("SELECT kamitime, oktoken, oktime FROM kamis WHERE kami = ?");
    $stmt->bind_param("s", $kami);
    $stmt->execute();
    $kami_result = $stmt->get_result();

    if ($kami_result->num_rows == 0) {
        echo json_encode(["code" => 400, "message" => "卡密不存在"], JSON_UNESCAPED_UNICODE);
        exit();
    }

    $kami_row = $kami_result->fetch_assoc();

    // 检查卡密是否已使用
    if ($kami_row['oktoken'] !== NULL) {
        echo json_encode([
            "code" => 400,
            "message" => "卡密已在" . $kami_row['oktime'] . "被使用"
        ], JSON_UNESCAPED_UNICODE);
        exit();
    }

    // 卡密可用，进行使用操作
    $current_time = date('Y-m-d H:i:s');
    $kamitime_days = intval($kami_row['kamitime']);

    // 更新卡密的使用状态
    $stmt = $mysqli->prepare("UPDATE kamis SET oktoken = ?, oktime = ?, kamicode = '1' WHERE kami = ?");
    $stmt->bind_param("sss", $token, $current_time, $kami);
    $stmt->execute();

    // 计算新的 VIP 到期时间
    if ($user['vipcode'] == '0' || $user['viptime'] == NULL || strtotime($user['viptime']) < time()) {
        // 用户当前不是会员，或会员已过期
        $new_viptime = date('Y-m-d H:i:s', strtotime("+$kamitime_days days"));
    } else {
        // 用户当前是会员，延长 VIP 到期时间
        $new_viptime = date('Y-m-d H:i:s', strtotime($user['viptime'] . " + $kamitime_days days"));
    }

    // 更新用户的 VIP 状态和 VIP 到期时间
    $stmt = $mysqli->prepare("UPDATE users SET vipcode = '1', viptime = ? WHERE token = ?");
    $stmt->bind_param("ss", $new_viptime, $token);
    $stmt->execute();

    // 返回成功信息
    echo json_encode([
        "code" => 200,
        "message" => "卡密使用成功",
        "user" => [
            "token" => $token,
            "viptime" => $new_viptime
        ]
    ], JSON_UNESCAPED_UNICODE);

    $stmt->close();
} else {
    // 参数缺失
    echo json_encode(["code" => 400, "message" => "请输入正确的token和kami参数"], JSON_UNESCAPED_UNICODE);
}

$mysqli->close();
?>
