// Modern Application Class
class App {
    constructor() {
        this.currentPage = 'dashboard';
        this.pages = {};
        this.isMobile = window.innerWidth <= 768;
        this.sidebarCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
        this.sidebarOpen = false;
        this.init();
    }

    init() {
        this.initElements();
        this.initSidebar();
        this.initNavigation();
        this.initResponsive();
        this.initKeyboardShortcuts();
        this.loadInitialPage();
        this.hideLoadingScreen();
    }

    initElements() {
        this.elements = {
            sidebar: utils.$('#sidebar'),
            sidebarToggle: utils.$('#sidebarToggle'),
            mobileMenuToggle: utils.$('#mobileMenuToggle'),
            mobileOverlay: utils.$('#mobileOverlay'),
            pageTitle: utils.$('#pageTitle'),
            breadcrumb: utils.$('#breadcrumb'),
            refreshBtn: utils.$('#refreshBtn'),
            settingsBtn: utils.$('#settingsBtn')
        };
    }

    initSidebar() {
        // Apply saved sidebar state
        if (this.sidebarCollapsed && !this.isMobile) {
            this.elements.sidebar.classList.add('collapsed');
        }

        // Sidebar toggle for desktop
        if (this.elements.sidebarToggle) {
            this.elements.sidebarToggle.addEventListener('click', () => {
                this.toggleSidebar();
            });
        }

        // Mobile menu toggle
        if (this.elements.mobileMenuToggle) {
            this.elements.mobileMenuToggle.addEventListener('click', () => {
                this.toggleMobileSidebar();
            });
        }

        // Mobile overlay
        if (this.elements.mobileOverlay) {
            this.elements.mobileOverlay.addEventListener('click', () => {
                this.closeMobileSidebar();
            });
        }

        // Update toggle icon
        this.updateSidebarToggleIcon();
    }

    toggleSidebar() {
        if (this.isMobile) {
            this.toggleMobileSidebar();
            return;
        }

        this.sidebarCollapsed = !this.sidebarCollapsed;
        this.elements.sidebar.classList.toggle('collapsed', this.sidebarCollapsed);
        
        // Save state
        localStorage.setItem('sidebarCollapsed', this.sidebarCollapsed);
        
        // Update icon
        this.updateSidebarToggleIcon();
        
        // Trigger resize event for charts
        setTimeout(() => {
            window.dispatchEvent(new Event('resize'));
        }, 300);
    }

    toggleMobileSidebar() {
        this.sidebarOpen = !this.sidebarOpen;
        this.elements.sidebar.classList.toggle('open', this.sidebarOpen);
        this.elements.mobileOverlay.classList.toggle('active', this.sidebarOpen);
        
        // Prevent body scroll when sidebar is open
        document.body.style.overflow = this.sidebarOpen ? 'hidden' : '';
    }

    closeMobileSidebar() {
        this.sidebarOpen = false;
        this.elements.sidebar.classList.remove('open');
        this.elements.mobileOverlay.classList.remove('active');
        document.body.style.overflow = '';
    }

    updateSidebarToggleIcon() {
        if (this.elements.sidebarToggle) {
            const icon = this.elements.sidebarToggle.querySelector('i');
            if (icon) {
                icon.className = this.sidebarCollapsed ? 
                    'fas fa-chevron-right' : 'fas fa-chevron-left';
            }
        }
    }

    initNavigation() {
        const navLinks = utils.$$('.nav-link');
        
        navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                
                const page = link.getAttribute('data-page');
                if (page && page !== this.currentPage) {
                    this.navigateToPage(page);
                }
                
                // Close mobile sidebar after navigation
                if (this.isMobile) {
                    this.closeMobileSidebar();
                }
            });
        });
    }

    navigateToPage(page) {
        // Update active nav link
        utils.$$('.nav-link').forEach(link => {
            link.classList.remove('active');
        });
        
        const activeLink = utils.$(`[data-page="${page}"]`);
        if (activeLink) {
            activeLink.classList.add('active');
        }

        // Hide all page contents
        utils.$$('.page-content').forEach(content => {
            content.classList.remove('active');
        });

        // Show target page content
        const pageContent = utils.$(`#${page}-content`);
        if (pageContent) {
            pageContent.classList.add('active');
        }

        // Update page title and breadcrumb
        this.updatePageHeader(page);

        // Load page if not already loaded
        if (!this.pages[page]) {
            this.loadPage(page);
        }

        this.currentPage = page;
    }

    updatePageHeader(page) {
        const pageInfo = {
            dashboard: { title: '仪表盘', breadcrumb: ['主页', '仪表盘'] },
            users: { title: '用户管理', breadcrumb: ['主页', '用户管理'] },
            cards: { title: '卡密管理', breadcrumb: ['主页', '卡密管理'] },
            statistics: { title: '数据统计', breadcrumb: ['主页', '数据统计'] },
            logs: { title: '操作日志', breadcrumb: ['主页', '操作日志'] },
            settings: { title: '系统设置', breadcrumb: ['主页', '系统设置'] }
        };

        const info = pageInfo[page] || { title: '未知页面', breadcrumb: ['主页'] };
        
        if (this.elements.pageTitle) {
            this.elements.pageTitle.textContent = info.title;
        }

        if (this.elements.breadcrumb) {
            this.elements.breadcrumb.innerHTML = info.breadcrumb
                .map((item, index) => {
                    if (index === info.breadcrumb.length - 1) {
                        return `<span>${item}</span>`;
                    }
                    return `<span>${item}</span><i class="breadcrumb-separator fas fa-chevron-right"></i>`;
                })
                .join('');
        }

        // Update document title
        document.title = `${info.title} - 系统管理后台`;
    }

    async loadPage(page) {
        try {
            loading.show(`加载${page}页面...`);
            
            // Simulate page loading (replace with actual page loading logic)
            await new Promise(resolve => setTimeout(resolve, 500));
            
            // Initialize page-specific functionality
            switch (page) {
                case 'dashboard':
                    if (window.Dashboard) {
                        this.pages[page] = new Dashboard();
                    }
                    break;
                case 'users':
                    if (window.Users) {
                        this.pages[page] = new Users();
                    }
                    break;
                case 'cards':
                    if (window.Cards) {
                        this.pages[page] = new Cards();
                    }
                    break;
                case 'statistics':
                    if (window.Statistics) {
                        this.pages[page] = new Statistics();
                    }
                    break;
                case 'logs':
                    if (window.Logs) {
                        this.pages[page] = new Logs();
                    }
                    break;
                case 'settings':
                    if (window.Settings) {
                        this.pages[page] = new Settings();
                    }
                    break;
            }
            
            loading.hide();
        } catch (error) {
            console.error(`Failed to load page ${page}:`, error);
            toast.error(`加载页面失败: ${error.message}`);
            loading.hide();
        }
    }

    initResponsive() {
        window.addEventListener('resize', () => {
            const wasMobile = this.isMobile;
            this.isMobile = window.innerWidth <= 768;
            
            if (wasMobile !== this.isMobile) {
                // Mobile/desktop transition
                if (this.isMobile) {
                    // Switched to mobile
                    this.elements.sidebar.classList.remove('collapsed');
                    this.closeMobileSidebar();
                } else {
                    // Switched to desktop
                    this.elements.sidebar.classList.remove('open');
                    this.elements.mobileOverlay.classList.remove('active');
                    document.body.style.overflow = '';
                    
                    if (this.sidebarCollapsed) {
                        this.elements.sidebar.classList.add('collapsed');
                    }
                }
                
                this.updateSidebarToggleIcon();
            }
        });
    }

    initKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Ctrl/Cmd + B: Toggle sidebar
            if ((e.ctrlKey || e.metaKey) && e.key === 'b') {
                e.preventDefault();
                this.toggleSidebar();
            }
            
            // Escape: Close mobile sidebar
            if (e.key === 'Escape' && this.isMobile && this.sidebarOpen) {
                this.closeMobileSidebar();
            }
        });
    }

    loadInitialPage() {
        // Load dashboard by default
        this.loadPage('dashboard');
    }

    hideLoadingScreen() {
        setTimeout(() => {
            const loadingOverlay = utils.$('#loadingOverlay');
            if (loadingOverlay) {
                loadingOverlay.style.opacity = '0';
                setTimeout(() => {
                    loadingOverlay.style.display = 'none';
                }, 300);
            }
        }, 800);
    }
}

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.app = new App();
});
